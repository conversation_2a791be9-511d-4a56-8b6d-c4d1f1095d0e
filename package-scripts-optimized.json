{"scripts": {"// OPTIMIZED SCRIPTS": "Performance-optimized linting and formatting", "// Core Quality Scripts": "", "lint:check": "eslint . --ext .ts,.tsx --config eslint.config.mjs", "lint:fix": "eslint . --ext .ts,.tsx --fix --config eslint.config.mjs", "lint:report": "eslint . --ext .ts,.tsx --format json --output-file eslint-report.json --config eslint.config.mjs", "lint:warn-only": "eslint . --ext .ts,.tsx --max-warnings 1000 --config eslint.config.mjs", "format": "prettier --write .", "format:check": "prettier --check .", "format:staged": "prettier --write --cache", "type-check": "tsc --noEmit", "// Performance Optimized Scripts": "", "code-quality": "pnpm run type-check && pnpm run lint:report && pnpm run format:check", "code-quality:fast": "pnpm run type-check && pnpm run lint:warn-only", "fix-all": "pnpm run lint:fix && pnpm run format", "fix-all:fast": "pnpm run lint:fix --quiet && pnpm run format --cache", "// Build Scripts": "", "prebuild": "pnpm run code-quality:fast", "build": "pnpm audit --audit-level moderate && node scripts/generate-sitemap-data.js && next build && cp -r public .next/standalone/", "build:production": "pnpm run fix-all && pnpm run code-quality && pnpm run build", "build:fast": "pnpm run prebuild && pnpm run build", "// Development Scripts": "", "dev": "next dev --turbo", "start": "next start", "// Tailwind Specific Scripts": "", "lint:tailwind": "eslint . --ext .ts,.tsx --config eslint.config.mjs | grep better-tailwindcss", "lint:no-tailwind": "eslint . --ext .ts,.tsx --config eslint.config.mjs | grep -v better-tailwindcss"}}