'use client'

import dynamic from 'next/dynamic'
import { Suspense } from 'react'

// Dynamically import HeroSection with Framer Motion
const HeroSection = dynamic(() => import('./HeroSection'), {
  ssr: false, // Disable SSR for Framer Motion components
  loading: () => (
    <section 
      className='relative w-full overflow-hidden bg-gray-900'
      style={{ 
        height: '80vh',
        minHeight: '600px',
        aspectRatio: '16/9'
      }}
    >
      {/* Static fallback during loading */}
      <video
        src='/videos/hero-loop.mp4'
        poster='/images/hero-image.webp'
        autoPlay
        muted
        loop
        playsInline
        preload='metadata'
        className='absolute inset-0 h-full w-full object-cover'
      />
      
      {/* Overlay gradient */}
      <div className='absolute inset-0 z-10 bg-gradient-to-b from-black/30 via-transparent to-black/40' />
      
      {/* Static title (no animation during loading) */}
      <div className='absolute bottom-20 z-20 flex w-full justify-center'>
        <h1 className='text-5xl font-extrabold text-white drop-shadow-lg select-none md:text-7xl'>
          The Full Pony Club Experience
        </h1>
      </div>
      
      {/* Static scroll prompt */}
      <div className='absolute bottom-6 left-1/2 z-30 flex -translate-x-1/2 flex-col items-center text-white select-none'>
        <span className='mb-2 text-lg font-semibold'>Adventure begins here</span>
        <svg
          className='h-8 w-8'
          fill='none'
          stroke='currentColor'
          strokeWidth='2'
          viewBox='0 0 24 24'
          xmlns='http://www.w3.org/2000/svg'
          aria-hidden='true'
        >
          <path strokeLinecap='round' strokeLinejoin='round' d='M19 9l-7 7-7-7'></path>
        </svg>
      </div>
    </section>
  ),
})

export default function DynamicHeroSection() {
  return (
    <Suspense fallback={null}>
      <HeroSection />
    </Suspense>
  )
}
