'use client'

import dynamic from 'next/dynamic'
import { Suspense } from 'react'

// Dynamically import GoogleReviews with no SSR
const GoogleReviews = dynamic(() => import('./GoogleReviews'), {
  ssr: false, // Disable SSR for third-party widget
  loading: () => (
    <div className='mt-6 mb-20 px-4 md:px-8'>
      <div className='mx-auto max-w-6xl rounded-2xl bg-white/90 p-4 shadow-md'>
        <div className='flex items-center justify-center h-32'>
          <div className='animate-pulse flex space-x-4'>
            <div className='rounded-full bg-gray-300 h-12 w-12'></div>
            <div className='flex-1 space-y-2 py-1'>
              <div className='h-4 bg-gray-300 rounded w-3/4'></div>
              <div className='space-y-2'>
                <div className='h-4 bg-gray-300 rounded'></div>
                <div className='h-4 bg-gray-300 rounded w-5/6'></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
})

export default function DynamicGoogleReviews() {
  return (
    <Suspense fallback={null}>
      <GoogleReviews />
    </Suspense>
  )
}
